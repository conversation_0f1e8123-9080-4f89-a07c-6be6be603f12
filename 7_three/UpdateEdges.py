import numpy as np
import random
import math

''' Functions to handle edges in our network. '''

''' 
Randomly deletes some number of edges
between min_edge_removal and max_edge_removal 
'''


def Delete(dyNetwork, min_edge_removal, max_edge_removal):
    # 获取边列表并确保是可抽样的序列
    edges = list(dyNetwork._network.edges())  # 关键修改：转换为list

    # 处理空图的情况
    if not edges:
        return

    # 计算实际要删除的边数（确保不超过现有边数）
    deletion_number = random.randint(
        min_edge_removal,
        min(max_edge_removal, len(edges))
    )

    # 处理无效删除数量
    if deletion_number <= 0:
        return

    try:
        # 安全抽样（edges已经是list）
        strip = random.sample(edges, k=deletion_number)

        # 保存边属性（如果需要）
        temp = []
        for s_edge, e_edge in strip:
            # 检查边是否存在（防御性编程）
            if dyNetwork._network.has_edge(s_edge, e_edge):
                edge_data = dyNetwork._network[s_edge][e_edge]
                temp.append((s_edge, e_edge, edge_data))

        # 执行删除
        if temp:
            dyNetwork._network.remove_edges_from([(s, e) for s, e, _ in temp])
            dyNetwork._stripped_list.extend(temp)

    except ValueError as e:
        print(f"Warning: Edge deletion failed - {str(e)}")
        # 可以选择记录日志或进行其他错误处理

''' 
Randomly restores some
edges we have deleted 
'''
def Restore(dyNetwork):
    restore_number = random.randint(0, len(dyNetwork._stripped_list))
    restore = random.choices(dyNetwork._stripped_list, k=restore_number)
    dyNetwork._network.add_edges_from(restore)
    
''' Randomly change edge weights '''
def Random_Walk(dyNetwork):
    for s_edge, e_edge in dyNetwork._network.edges():
        try:
            changed = random.randint(-2, 2) + dyNetwork._network[s_edge][e_edge]['edge_delay']
            dyNetwork._network[s_edge][e_edge]['edge_delay'] = max(changed, 1)
        except:
            print(s_edge, e_edge)
            
''' 
Change edge weights so that the edge weight 
changes will be roughly sinusoidal across the simulation
'''
def Sinusoidal(dyNetwork):
    for s_edge, e_edge in dyNetwork._network.edges():
        dyNetwork._network[s_edge][e_edge]['edge_delay'] = max(1, int(dyNetwork._network[s_edge][e_edge]['initial_weight']* (1 + 0.5 * math.sin(dyNetwork._network[s_edge][e_edge]['sine_state']))))
        dyNetwork._network[s_edge][e_edge]['sine_state'] += math.pi/6

''' 
Not in use. If it were used the edge weight would be the
average of the number of packets in each
queue of the endpoints of the edge. 
'''
def Average(dyNetwork):
    for node1, node2 in dyNetwork._network.edges(data = False):
        tot_queue1 = dyNetwork._network.nodes[node1]['sending_queue']
        tot_queue2 = dyNetwork._network.nodes[node2]['sending_queue']
        avg = np.avg([tot_queue1, tot_queue2])
        dyNetwork._network[node1][node2]['edge_delay'] = avg
        del tot_queue1, tot_queue2