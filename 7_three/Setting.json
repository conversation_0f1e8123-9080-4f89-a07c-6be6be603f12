{"NETWORK": {"number nodes": 50, "edge degree": 3, "holding capacity": 150, "sending capacity": 20, "initial num packets": 5000, "max_additional_packets": 500000, "max_edge_weight": 10, "min_edge_removal": 5, "max_edge_removal": 10, "edge_change_type": "sinusoidal", "network_type": "satellite", "num_planes": 5, "sats_per_plane": 10, "altitude": 1000, "inclination": 53}, "DQN": {"take_queue_size_as_input": 1, "memory_batch_size": 16, "memory_bank_size": 1000, "optimizer_learning_rate": 0.005, "optimize_per_episode": 1}, "AGENT": {"epsilon": 0.8, "decay_epsilon_rate": 0.998, "gamma_for_next_q_val": 0.9, "use_random_sample_memory": 1, "use_most_recent_memory": 0, "use_priority_memory": 0, "use_teacher_guidance": true, "teacher_probability": 0.5, "teacher_decay_rate": 0.8, "teacher_min_probability": 0.05}, "Simulation": {"training_episodes": 500, "max_allowed_time_step_per_episode": 500, "num_time_step_to_update_target_network": 2, "test_network_load_min": 200, "test_network_load_max": 3000, "test_network_load_step_size": 200, "test_trials_per_load": 1, "learning_plot": 1, "test_diff_network_load_plot": 1, "plot_routing_network": 0}}