import json
import numpy as np
import networkx as nx
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import math
import os
import random
import argparse

def create_satellite_network(num_planes=5, sats_per_plane=10, altitude=1000, inclination=53):
    """
    创建卫星星座网络
    
    Args:
        num_planes: 轨道平面数量
        sats_per_plane: 每个平面的卫星数量
        altitude: 轨道高度(km)
        inclination: 轨道倾角(度)
    
    Returns:
        networkx.Graph: 卫星网络图
    """
    # 创建一个空图
    network = nx.Graph()
    
    # 地球半径(km)
    earth_radius = 6371.0
    # 轨道半径
    orbit_radius = earth_radius + altitude
    
    # 创建卫星节点
    sat_positions = {}  # 存储卫星位置
    
    # 为每个轨道平面分配不同的颜色
    plane_colors = {}
    
    sat_id = 0
    for plane in range(num_planes):
        # 计算升交点赤经(RAAN)
        raan = (360.0 / num_planes) * plane
        
        # 为每个轨道平面分配一个颜色
        plane_colors[plane] = plt.cm.jet(plane / num_planes)
        
        for i in range(sats_per_plane):
            # 计算平面内的相位角
            phase = (360.0 / sats_per_plane) * i
            
            # 计算卫星位置(直角坐标)
            # 首先在轨道平面内计算位置
            x_orbit = orbit_radius * np.cos(np.radians(phase))
            y_orbit = orbit_radius * np.sin(np.radians(phase))
            
            # 然后应用轨道倾角和RAAN旋转
            x = x_orbit * np.cos(np.radians(raan))
            y = x_orbit * np.sin(np.radians(raan))
            z = y_orbit * np.sin(np.radians(inclination))
            
            # 存储位置
            sat_positions[sat_id] = (x, y, z)
            
            # 添加节点，包括所属轨道平面信息
            network.add_node(sat_id, pos=(x, y, z), plane=plane, phase=phase)
            sat_id += 1
    
    # 为每颗卫星找到最近的四颗邻居并创建链路
    for sat1_id in range(sat_id):
        # 获取卫星所在的轨道平面
        plane1 = network.nodes[sat1_id]['plane']
        
        # 计算与所有其他卫星的距离
        intra_plane_distances = []  # 同一轨道平面内的卫星
        inter_plane_distances = []  # 不同轨道平面的卫星
        
        for sat2_id in range(sat_id):
            if sat1_id != sat2_id:
                # 获取第二颗卫星所在的轨道平面
                plane2 = network.nodes[sat2_id]['plane']
                
                # 计算欧几里得距离
                pos1 = np.array(sat_positions[sat1_id])
                pos2 = np.array(sat_positions[sat2_id])
                dist = np.linalg.norm(pos1 - pos2)
                
                # 根据是否在同一轨道平面分类
                if plane1 == plane2:
                    intra_plane_distances.append((sat2_id, dist))
                else:
                    inter_plane_distances.append((sat2_id, dist))
        
        # 按距离排序
        intra_plane_distances.sort(key=lambda x: x[1])
        inter_plane_distances.sort(key=lambda x: x[1])
        
        # 连接同一轨道平面内的最近两颗卫星
        for i in range(min(2, len(intra_plane_distances))):
            neighbor_id, distance = intra_plane_distances[i]
            if not network.has_edge(sat1_id, neighbor_id):
                network.add_edge(sat1_id, neighbor_id, distance=distance, link_type='intra')
        
        # 连接不同轨道平面的最近两颗卫星
        for i in range(min(2, len(inter_plane_distances))):
            neighbor_id, distance = inter_plane_distances[i]
            if not network.has_edge(sat1_id, neighbor_id):
                network.add_edge(sat1_id, neighbor_id, distance=distance, link_type='inter')
    
    return network, sat_positions, plane_colors

def visualize_satellite_network(network, positions, plane_colors, highlight_path=None, show_earth=True, show_orbits=True, view_elev=20, view_azim=30, save_path="satellite_network_3d.png"):
    """可视化卫星网络（三维图形）"""
    # 创建一个新的图形和3D坐标轴
    fig = plt.figure(figsize=(16, 14))
    ax = fig.add_subplot(111, projection='3d')
    
    # 如果需要显示地球
    if show_earth:
        # 创建地球表面
        earth_radius = 6371.0  # 地球半径(km)
        u = np.linspace(0, 2 * np.pi, 100)
        v = np.linspace(0, np.pi, 100)
        x = earth_radius * np.outer(np.cos(u), np.sin(v))
        y = earth_radius * np.outer(np.sin(u), np.sin(v))
        z = earth_radius * np.outer(np.ones(np.size(u)), np.cos(v))
        
        # 绘制地球表面 - 使用半透明蓝色
        ax.plot_surface(x, y, z, color='skyblue', alpha=0.3, rstride=4, cstride=4)
    
    # 如果需要显示轨道
    if show_orbits:
        # 获取所有轨道平面
        planes = set(nx.get_node_attributes(network, 'plane').values())
        
        for plane in planes:
            # 获取该轨道平面上的所有卫星
            plane_satellites = [node for node, attrs in network.nodes(data=True) if attrs.get('plane') == plane]
            
            if plane_satellites:
                # 获取该平面上卫星的位置
                plane_positions = [positions[sat] for sat in plane_satellites]
                
                # 按相位角排序
                phases = [network.nodes[sat]['phase'] for sat in plane_satellites]
                sorted_indices = np.argsort(phases)
                sorted_positions = [plane_positions[i] for i in sorted_indices]
                
                # 闭合轨道
                sorted_positions.append(sorted_positions[0])
                
                # 提取坐标
                xs = [pos[0] for pos in sorted_positions]
                ys = [pos[1] for pos in sorted_positions]
                zs = [pos[2] for pos in sorted_positions]
                
                # 绘制轨道 - 使用与轨道平面对应的颜色
                ax.plot(xs, ys, zs, '-', color=plane_colors[plane], alpha=0.5, linewidth=1)
    
    # 绘制节点 - 使用与轨道平面对应的颜色
    for node in network.nodes():
        plane = network.nodes[node]['plane']
        pos = positions[node]
        ax.scatter([pos[0]], [pos[1]], [pos[2]], color=plane_colors[plane], s=50, alpha=0.8)
    
    # 添加节点标签 - 只显示部分节点的标签以减少视觉混乱
    for node in network.nodes():
        # 只显示每五个节点的标签
        if node % 5 == 0:
            pos = positions[node]
            ax.text(pos[0], pos[1], pos[2], str(node), fontsize=8)
    
    # 绘制边 - 区分同一轨道平面内的链路和跨轨道平面的链路
    for u, v, data in network.edges(data=True):
        pos1 = positions[u]
        pos2 = positions[v]
        x = [pos1[0], pos2[0]]
        y = [pos1[1], pos2[1]]
        z = [pos1[2], pos2[2]]
        
        if data.get('link_type') == 'intra':
            # 同一轨道平面内的链路 - 使用实线
            plane = network.nodes[u]['plane']
            ax.plot(x, y, z, '-', color=plane_colors[plane], alpha=0.7, linewidth=1)
        else:
            # 跨轨道平面的链路 - 使用虚线
            ax.plot(x, y, z, '--', color='gray', alpha=0.5, linewidth=1)
    
    # 高亮显示路径
    if highlight_path and len(highlight_path) > 1:
        for i in range(len(highlight_path) - 1):
            node1 = highlight_path[i]
            node2 = highlight_path[i + 1]
            if node1 in positions and node2 in positions:
                pos1 = positions[node1]
                pos2 = positions[node2]
                x = [pos1[0], pos2[0]]
                y = [pos1[1], pos2[1]]
                z = [pos1[2], pos2[2]]
                ax.plot(x, y, z, 'magenta', alpha=1.0, linewidth=2)
        
        # 特别标记路径的起点和终点
        start_node = highlight_path[0]
        end_node = highlight_path[-1]
        
        start_pos = positions[start_node]
        end_pos = positions[end_node]
        
        ax.scatter([start_pos[0]], [start_pos[1]], [start_pos[2]], color='green', s=200, label='Start')
        ax.scatter([end_pos[0]], [end_pos[1]], [end_pos[2]], color='blue', s=200, label='End')
        
        # 添加路径信息
        path_length = len(highlight_path) - 1
        path_info = f"Path: {start_node} → {end_node}, Hops: {path_length}"
        plt.figtext(0.5, 0.01, path_info, ha='center', fontsize=12)
    
    # 设置图形标题和轴标签
    ax.set_title('LEO Satellite Constellation Network', fontsize=16)
    ax.set_xlabel('X (km)')
    ax.set_ylabel('Y (km)')
    ax.set_zlabel('Z (km)')
    
    # 添加图例
    from matplotlib.lines import Line2D
    legend_elements = [
        Line2D([0], [0], color='gray', linestyle='--', label='Inter-plane Links'),
        Line2D([0], [0], color=list(plane_colors.values())[0], label='Intra-plane Links')
    ]
    if highlight_path:
        legend_elements.append(Line2D([0], [0], color='magenta', label='Routing Path'))
    
    ax.legend(handles=legend_elements, loc='upper right')
    
    # 添加网络信息
    num_planes = len(set(nx.get_node_attributes(network, 'plane').values()))
    sats_per_plane = len(network.nodes()) // num_planes
    info_text = f"Constellation: {num_planes} planes × {sats_per_plane} satellites"
    plt.figtext(0.5, 0.95, info_text, ha='center', fontsize=14)
    
    # 调整视角
    ax.view_init(elev=view_elev, azim=view_azim)
    
    # 设置平等的坐标比例，使图形不变形
    ax.set_box_aspect([1, 1, 1])
    
    # 保存图像
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    # 显示图形
    plt.show()

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Visualize a satellite constellation network in 3D')
    parser.add_argument('--planes', type=int, default=5, help='Number of orbital planes')
    parser.add_argument('--sats', type=int, default=10, help='Number of satellites per plane')
    parser.add_argument('--altitude', type=float, default=1000, help='Orbit altitude in km')
    parser.add_argument('--inclination', type=float, default=53, help='Orbit inclination in degrees')
    parser.add_argument('--earth', action='store_true', help='Show Earth')
    parser.add_argument('--orbits', action='store_true', help='Show orbital paths')
    parser.add_argument('--elev', type=float, default=20, help='Elevation angle for view')
    parser.add_argument('--azim', type=float, default=30, help='Azimuth angle for view')
    parser.add_argument('--output', type=str, default='satellite_network_3d.png', help='Output file name')
    parser.add_argument('--path', action='store_true', help='Show a random routing path')
    
    args = parser.parse_args()
    
    # 创建卫星网络
    network, positions, plane_colors = create_satellite_network(
        num_planes=args.planes,
        sats_per_plane=args.sats,
        altitude=args.altitude,
        inclination=args.inclination
    )
    
    # 如果需要显示路径
    path = None
    if args.path:
        # 随机选择一条路径进行高亮显示
        nodes = list(network.nodes())
        start_node = random.choice(nodes)
        end_node = random.choice([n for n in nodes if n != start_node])
        
        try:
            # 尝试找到最短路径
            path = nx.shortest_path(network, source=start_node, target=end_node)
            print(f"显示从节点 {start_node} 到节点 {end_node} 的路径")
        except nx.NetworkXNoPath:
            path = None
            print("未找到路径，只显示网络结构")
    
    # 可视化网络
    visualize_satellite_network(
        network, 
        positions, 
        plane_colors,
        highlight_path=path,
        show_earth=args.earth,
        show_orbits=args.orbits,
        view_elev=args.elev,
        view_azim=args.azim,
        save_path=args.output
    )
    
    print(f"卫星网络创建完成，共有 {len(network.nodes())} 个节点和 {len(network.edges())} 条边")
    print(f"图像已保存为 {args.output}")

if __name__ == "__main__":
    # 如果没有命令行参数，使用默认值
    if len(os.sys.argv) == 1:
        # 从Setting.json读取卫星网络参数
        try:
            with open('Setting.json', 'r') as f:
                setting = json.load(f)
                
            num_planes = setting['NETWORK'].get('num_planes', 5)
            sats_per_plane = setting['NETWORK'].get('sats_per_plane', 10)
            altitude = setting['NETWORK'].get('altitude', 1000)
            inclination = setting['NETWORK'].get('inclination', 53)
            
            # 创建卫星网络
            network, positions, plane_colors = create_satellite_network(
                num_planes=num_planes,
                sats_per_plane=sats_per_plane,
                altitude=altitude,
                inclination=inclination
            )
            
            # 随机选择一条路径进行高亮显示
            nodes = list(network.nodes())
            start_node = random.choice(nodes)
            end_node = random.choice([n for n in nodes if n != start_node])
            
            try:
                # 尝试找到最短路径
                path = nx.shortest_path(network, source=start_node, target=end_node)
                print(f"显示从节点 {start_node} 到节点 {end_node} 的路径")
            except nx.NetworkXNoPath:
                path = None
                print("未找到路径，只显示网络结构")
            
            # 可视化网络
            visualize_satellite_network(
                network, 
                positions, 
                plane_colors,
                highlight_path=path,
                show_earth=True,
                show_orbits=True
            )
            
            print(f"卫星网络创建完成，共有 {len(network.nodes())} 个节点和 {len(network.edges())} 条边")
            print(f"图像已保存为 satellite_network_3d.png")
        except Exception as e:
            print(f"读取Setting.json失败: {e}")
            main()
    else:
        main()
