import json
import numpy as np
import networkx as nx
import math
import os
import random
import torch
import torch.nn.functional as F
import argparse
import time
import copy
from collections import deque

# 设置Matplotlib后端为qtagg
import matplotlib
matplotlib.use('qtagg')

import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from our_env import dynetworkEnv
from our_agent import QAgent, get_shortest_path_action

def create_satellite_network(num_planes=5, sats_per_plane=10, altitude=1000, inclination=53):
    """创建卫星星座网络"""
    # 创建一个空图
    network = nx.Graph()

    # 地球半径(km)
    earth_radius = 6371.0
    # 轨道半径
    orbit_radius = earth_radius + altitude

    # 创建卫星节点
    sat_positions = {}  # 存储卫星位置

    sat_id = 0
    for plane in range(num_planes):
        # 计算升交点赤经(RAAN)
        raan = (360.0 / num_planes) * plane

        for i in range(sats_per_plane):
            # 计算平面内的相位角
            phase = (360.0 / sats_per_plane) * i

            # 计算卫星位置(直角坐标)
            # 首先在轨道平面内计算位置
            x_orbit = orbit_radius * np.cos(np.radians(phase))
            y_orbit = orbit_radius * np.sin(np.radians(phase))

            # 然后应用轨道倾角和RAAN旋转
            x = x_orbit * np.cos(np.radians(raan))
            y = x_orbit * np.sin(np.radians(raan))
            z = y_orbit * np.sin(np.radians(inclination))

            # 存储位置
            sat_positions[sat_id] = (x, y, z)

            # 添加节点
            network.add_node(sat_id, pos=(x, y, z), plane=plane, phase=phase)
            sat_id += 1

    # 为每颗卫星找到最近的四颗邻居并创建链路
    for sat1_id in range(sat_id):
        # 计算与所有其他卫星的距离
        distances = []
        for sat2_id in range(sat_id):
            if sat1_id != sat2_id:
                # 计算欧几里得距离
                pos1 = np.array(sat_positions[sat1_id])
                pos2 = np.array(sat_positions[sat2_id])
                dist = np.linalg.norm(pos1 - pos2)
                distances.append((sat2_id, dist))

        # 按距离排序
        distances.sort(key=lambda x: x[1])

        # 连接到最近的四颗卫星
        for i in range(min(4, len(distances))):
            neighbor_id, distance = distances[i]
            # 避免重复添加边
            if not network.has_edge(sat1_id, neighbor_id):
                # 将距离转换为延迟（假设光速传播）
                delay = max(1, int((distance / 300000.0) * 1000))  # 单位为毫秒
                network.add_edge(sat1_id, neighbor_id, distance=distance, edge_delay=delay)

    return network, sat_positions

def simulate_dqn_routing(env, agent, source, destination, max_steps=100):
    """使用DQN模型模拟从源节点到目标节点的路由过程"""
    # 跟踪路径
    path = [source]
    current_node = source
    steps = 0

    # 模拟路由过程
    while current_node != destination and steps < max_steps:
        # 获取当前状态
        state = F.one_hot(torch.tensor([current_node]), env.nnodes)
        if env.input_q_size:
            queue_size = len(env.dynetwork._network.nodes[current_node]['sending_queue'])
            size = torch.tensor([queue_size]).unsqueeze(0)
            state = torch.cat((state, size), dim=1)

        # 获取邻居
        neighbors = list(env.dynetwork._network.neighbors(current_node))
        if not neighbors:
            print(f"节点 {current_node} 没有邻居，无法继续路由")
            break

        # 使用DQN模型决策下一步
        next_node = agent.act(
            env.dqn[destination],
            state,
            neighbors,
            current_node=current_node,
            destination_node=destination,
            network=env.dynetwork._network
        )

        # 更新路径
        path.append(next_node)
        current_node = next_node
        steps += 1

    # 检查是否到达目标
    if current_node == destination:
        print(f"成功到达目标！共经过 {steps} 步")
    else:
        print(f"未能到达目标。已行进 {steps} 步")

    return path

def get_shortest_path(network, source, destination):
    """计算最短路径作为比较基准"""
    try:
        path = nx.shortest_path(network, source=source, target=destination, weight='edge_delay')
        return path
    except nx.NetworkXNoPath:
        print(f"无法找到从节点 {source} 到节点 {destination} 的路径")
        return None

def simulate_link_failures(network, failure_prob=0.1, recovery_prob=0.05):
    """模拟链路断开和恢复

    Args:
        network: 网络图
        failure_prob: 链路断开概率
        recovery_prob: 链路恢复概率

    Returns:
        failed_edges: 新断开的链路列表
        recovered_edges: 新恢复的链路列表
    """
    # 创建网络的副本以避免直接修改原始网络
    network_copy = network.copy()

    # 获取当前所有边
    current_edges = list(network_copy.edges())

    # 获取已断开的边（如果有属性）
    if not hasattr(network_copy, 'failed_edges'):
        network_copy.failed_edges = []

    failed_edges = []
    recovered_edges = []

    # 模拟链路断开
    for edge in current_edges:
        if random.random() < failure_prob:
            network_copy.remove_edge(*edge)
            failed_edges.append(edge)
            if hasattr(network_copy, 'failed_edges'):
                network_copy.failed_edges.append(edge)

    # 模拟链路恢复
    if hasattr(network_copy, 'failed_edges'):
        for edge in list(network_copy.failed_edges):
            if random.random() < recovery_prob:
                if edge not in network_copy.edges():
                    network_copy.add_edge(*edge)
                    # 恢复边的属性（如果有原始属性）
                    if edge in network.edges():
                        for key, value in network.edges[edge].items():
                            network_copy.edges[edge][key] = value
                    recovered_edges.append(edge)
                    network_copy.failed_edges.remove(edge)

    return network_copy, failed_edges, recovered_edges

def calculate_metrics(env, packets_sent, packets_delivered, delivery_times):
    """计算性能指标

    Args:
        env: 环境对象
        packets_sent: 发送的数据包数量
        packets_delivered: 成功传递的数据包数量
        delivery_times: 数据包传递时间列表

    Returns:
        metrics: 包含丢包率和平均时延的字典
    """
    metrics = {}

    # 计算丢包率
    if packets_sent > 0:
        packet_loss_rate = 1.0 - (packets_delivered / packets_sent)
        metrics['packet_loss_rate'] = packet_loss_rate
    else:
        metrics['packet_loss_rate'] = 0.0

    # 计算平均时延
    if delivery_times:
        avg_delay = sum(delivery_times) / len(delivery_times)
        metrics['avg_delay'] = avg_delay
    else:
        metrics['avg_delay'] = 0.0

    return metrics

def visualize_routing(network, positions, dqn_path, shortest_path=None, source=None, destination=None, show_earth=True):
    """可视化路由路径"""
    # 创建图形和坐标轴
    fig = plt.figure(figsize=(15, 12))
    ax = fig.add_subplot(111, projection='3d')

    # 如果需要显示地球
    if show_earth:
        # 创建地球表面
        earth_radius = 6371.0  # 地球半径(km)
        u = np.linspace(0, 2 * np.pi, 100)
        v = np.linspace(0, np.pi, 100)
        x = earth_radius * np.outer(np.cos(u), np.sin(v))
        y = earth_radius * np.outer(np.sin(u), np.sin(v))
        z = earth_radius * np.outer(np.ones(np.size(u)), np.cos(v))

        # 绘制地球表面 - 使用半透明蓝色
        ax.plot_surface(x, y, z, color='skyblue', alpha=0.2, rstride=4, cstride=4)

    # 绘制所有节点
    for node in network.nodes():
        pos = positions[node]
        if node == source:
            # 起点 - 绿色
            ax.scatter([pos[0]], [pos[1]], [pos[2]], color='green', s=100, label='Source' if node == list(network.nodes())[0] else "")
        elif node == destination:
            # 终点 - 红色
            ax.scatter([pos[0]], [pos[1]], [pos[2]], color='red', s=100, label='Destination' if node == list(network.nodes())[0] else "")
        elif node in dqn_path:
            # DQN路径上的节点 - 蓝色
            ax.scatter([pos[0]], [pos[1]], [pos[2]], color='blue', s=50)
        else:
            # 其他节点 - 灰色
            ax.scatter([pos[0]], [pos[1]], [pos[2]], color='gray', s=20, alpha=0.5)

    # 绘制所有边
    for edge in network.edges():
        pos1 = positions[edge[0]]
        pos2 = positions[edge[1]]
        x = [pos1[0], pos2[0]]
        y = [pos1[1], pos2[1]]
        z = [pos1[2], pos2[2]]

        # 普通边 - 灰色细线
        ax.plot(x, y, z, 'gray', linewidth=0.5, alpha=0.3)

    # 绘制DQN路径
    if dqn_path and len(dqn_path) > 1:
        for i in range(len(dqn_path) - 1):
            node1 = dqn_path[i]
            node2 = dqn_path[i + 1]
            pos1 = positions[node1]
            pos2 = positions[node2]
            x = [pos1[0], pos2[0]]
            y = [pos1[1], pos2[1]]
            z = [pos1[2], pos2[2]]

            # DQN路径 - 蓝色粗线
            ax.plot(x, y, z, 'blue', linewidth=2, alpha=0.8, label='DQN Path' if i == 0 else "")

    # 绘制最短路径（如果有）
    if shortest_path and len(shortest_path) > 1:
        for i in range(len(shortest_path) - 1):
            node1 = shortest_path[i]
            node2 = shortest_path[i + 1]
            pos1 = positions[node1]
            pos2 = positions[node2]
            x = [pos1[0], pos2[0]]
            y = [pos1[1], pos2[1]]
            z = [pos1[2], pos2[2]]

            # 最短路径 - 红色虚线
            ax.plot(x, y, z, 'red', linestyle='--', linewidth=2, alpha=0.8, label='Shortest Path' if i == 0 else "")

    # 添加标题和标签
    ax.set_title('DQN Routing in Satellite Constellation')
    ax.set_xlabel('X (km)')
    ax.set_ylabel('Y (km)')
    ax.set_zlabel('Z (km)')

    # 添加路径信息
    if dqn_path:
        dqn_info = f"DQN Path: {len(dqn_path)-1} hops"
        if shortest_path:
            shortest_info = f"Shortest Path: {len(shortest_path)-1} hops"
            plt.figtext(0.5, 0.01, f"{dqn_info} | {shortest_info}", ha='center', fontsize=12)
        else:
            plt.figtext(0.5, 0.01, dqn_info, ha='center', fontsize=12)

    # 添加图例
    handles, labels = ax.get_legend_handles_labels()
    by_label = dict(zip(labels, handles))
    ax.legend(by_label.values(), by_label.keys(), loc='upper right')

    # 调整视角
    ax.view_init(elev=20, azim=30)

    # 设置平等的坐标比例
    ax.set_box_aspect([1, 1, 1])

    # 保存图像
    plt.savefig('dqn_routing_visualization.png', dpi=300, bbox_inches='tight')
    print(f"图像已保存为: dqn_routing_visualization.png")

    # 不使用plt.show()，因为它可能会在非交互式后端下引发警告

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Visualize DQN routing in satellite constellation')
    parser.add_argument('--source', type=int, help='Source node ID (random if not specified)')
    parser.add_argument('--destination', type=int, help='Destination node ID (random if not specified)')
    parser.add_argument('--planes', type=int, default=5, help='Number of orbital planes')
    parser.add_argument('--sats', type=int, default=10, help='Number of satellites per plane')
    parser.add_argument('--altitude', type=float, default=1000, help='Orbit altitude in km')
    parser.add_argument('--inclination', type=float, default=53, help='Orbit inclination in degrees')
    parser.add_argument('--compare', action='store_true', help='Compare with shortest path')
    parser.add_argument('--earth', action='store_true', help='Show Earth')

    args = parser.parse_args()

    # 加载配置
    with open('Setting.json') as f:
        setting = json.load(f)

    # 创建环境和智能体
    env = dynetworkEnv()
    agent = QAgent(env.dynetwork)

    # 禁用探索
    agent.config['epsilon'] = 0.0

    # 创建卫星网络
    num_planes = args.planes
    sats_per_plane = args.sats
    altitude = args.altitude
    inclination = args.inclination

    network, positions = create_satellite_network(
        num_planes=num_planes,
        sats_per_plane=sats_per_plane,
        altitude=altitude,
        inclination=inclination
    )

    # 更新环境的网络
    for node in network.nodes():
        if node not in env.dynetwork._network.nodes():
            env.dynetwork._network.add_node(node)
            # 添加必要的节点属性
            env.dynetwork._network.nodes[node]['receiving_queue'] = []
            env.dynetwork._network.nodes[node]['sending_queue'] = []
            env.dynetwork._network.nodes[node]['max_receive_capacity'] = env.max_queue
            env.dynetwork._network.nodes[node]['max_send_capacity'] = env.max_transmit
            env.dynetwork._network.nodes[node]['congestion_measure'] = env.max_queue
            env.dynetwork._network.nodes[node]['max_queue_len'] = 0
            env.dynetwork._network.nodes[node]['avg_q_len_array'] = 0
            env.dynetwork._network.nodes[node]['growth'] = 0
            env.dynetwork._network.nodes[node]['pos'] = positions[node]

    # 更新边
    for edge in network.edges(data=True):
        node1, node2, data = edge
        if not env.dynetwork._network.has_edge(node1, node2):
            env.dynetwork._network.add_edge(node1, node2)
            # 添加必要的边属性
            env.dynetwork._network[node1][node2]['edge_delay'] = data.get('edge_delay', 1)
            env.dynetwork._network[node1][node2]['num_traversed'] = 0
            env.dynetwork._network[node1][node2]['sine_state'] = random.uniform(0, math.pi)
            env.dynetwork._network[node1][node2]['initial_weight'] = data.get('edge_delay', 1)

    # 更新环境的节点数
    env.nnodes = len(network.nodes())

    # 随机选择源节点和目标节点（如果未指定）
    nodes = list(network.nodes())
    source = args.source if args.source is not None else random.choice(nodes)

    # 确保目标节点与源节点不同
    if args.destination is not None:
        destination = args.destination
        if destination == source:
            print("警告：目标节点与源节点相同，随机选择新的目标节点")
            destination = random.choice([n for n in nodes if n != source])
    else:
        destination = random.choice([n for n in nodes if n != source])

    print(f"源节点: {source}, 目标节点: {destination}")

    # 使用DQN模型模拟路由
    dqn_path = simulate_dqn_routing(env, agent, source, destination)

    # 如果需要比较，计算最短路径
    shortest_path = None
    if args.compare:
        shortest_path = get_shortest_path(network, source, destination)
        if shortest_path:
            print(f"最短路径长度: {len(shortest_path)-1} 跳")
            print(f"最短路径: {' -> '.join(map(str, shortest_path))}")

    # 可视化路由
    visualize_routing(
        network,
        positions,
        dqn_path,
        shortest_path=shortest_path,
        source=source,
        destination=destination,
        show_earth=args.earth
    )

    # 打印路径
    print(f"DQN路由路径: {' -> '.join(map(str, dqn_path))}")

if __name__ == "__main__":
    main()
