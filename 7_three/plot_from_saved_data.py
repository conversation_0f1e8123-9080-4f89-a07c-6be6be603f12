#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从保存的仿真数据重新绘制图表的脚本

使用方法:
python plot_from_saved_data.py <数据文件路径>

例如:
python plot_from_saved_data.py simulation_data/simulation_20231201_143022/simulation_data.pkl
"""

import sys
import os
import pickle
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import scipy.interpolate as interp
from collections import defaultdict

matplotlib.use('Agg')

def load_simulation_data(data_path):
    """从文件加载仿真数据"""
    if data_path.endswith('.pkl'):
        with open(data_path, 'rb') as f:
            return pickle.load(f)
    elif data_path.endswith('.json'):
        with open(data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            # 将字典转换为defaultdict
            data['metrics_dqn'] = defaultdict(list, data['metrics_dqn'])
            data['metrics_sp'] = defaultdict(list, data['metrics_sp'])
            data['metrics_cbdr'] = defaultdict(list, data['metrics_cbdr'])
            return data
    else:
        raise ValueError("不支持的文件格式，请使用.pkl或.json文件")

def plot_comparison_from_data(network_load, trials, metrics_dqn, metrics_sp, metrics_cbdr, results_dir):
    """从保存的数据重新绘制DQN、最短路径和CBDR的性能对比图"""
    # 设置中文字体和字体大小
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams.update({
        'font.size': 20,           # 基础字体大小
        'axes.titlesize': 24,      # 标题字体大小
        'axes.labelsize': 22,      # 坐标轴标签字体大小
        'xtick.labelsize': 18,     # x轴刻度字体大小
        'ytick.labelsize': 18,     # y轴刻度字体大小
        'legend.fontsize': 18      # 图例字体大小
    })
    
    metrics = [
        ("平均时延", 'avg_deliv', "平均时延 (ms)"),
        ("最大队列长度", 'maxNumPkts', "每个节点的最大数据包数量"),
        ("平均队列长度", 'avg_q_len', "每个节点的平均数据包数量"),
        ("丢包率", 'packet_loss_rate', "丢包率 (%)")
    ]

    for title, metric_key, ylabel in metrics:
        plt.figure(figsize=(12, 7))
        plt.title(f"{title}")

        # 绘制三种算法的数据
        for algo, metrics_dict, color, marker in [
            ('DQN', metrics_dqn, 'blue', 'o'),
            ('最短路径', metrics_sp, 'red', '^'),
            ('CBDR', metrics_cbdr, 'green', 's')
        ]:
            data = metrics_dict[metric_key]

            # 移除任何 NaN 或 Inf 值
            valid_data = []
            valid_loads = []
            for d, l in zip(data, np.repeat(network_load, trials)):
                if np.isfinite(d):  # 只保留有限的数值
                    valid_data.append(d)
                    valid_loads.append(l)

            if not valid_data:  # 如果没有有效数据，跳过这个指标
                print(f"Warning: No valid data for {algo} - {title}")
                continue

            # 散点图
            plt.scatter(valid_loads, valid_data,
                       alpha=0.3, color=color, marker=marker)

            # 计算平均值
            unique_loads = np.unique(valid_loads)
            mean_values = []
            std_values = []

            for ul in unique_loads:
                values = [d for d, l in zip(valid_data, valid_loads) if l == ul]
                if values:  # 确保有数据
                    mean_values.append(np.mean(values))
                    std_values.append(np.std(values))
                else:
                    mean_values.append(np.nan)
                    std_values.append(np.nan)

            # 移除任何 NaN 值以进行插值
            valid_indices = ~np.isnan(mean_values)
            if np.sum(valid_indices) > 3:  # 需要至少4个点才能进行三次样条插值
                valid_loads_for_spline = unique_loads[valid_indices]
                valid_means_for_spline = np.array(mean_values)[valid_indices]
                valid_stds_for_spline = np.array(std_values)[valid_indices]

                # 平滑曲线（减少插值点数以降低平滑度）
                x_smooth = np.linspace(min(valid_loads_for_spline),
                                     max(valid_loads_for_spline), 30)  # 从50减少到30
                try:
                    # 使用较低阶数的样条插值来避免过拟合
                    spline = interp.make_interp_spline(valid_loads_for_spline,
                                                      valid_means_for_spline,
                                                      k=min(2, len(valid_loads_for_spline)-1))  # 从3减少到2
                    y_smooth = spline(x_smooth)

                    # 对标准差也进行插值
                    std_spline = interp.make_interp_spline(valid_loads_for_spline,
                                                          valid_stds_for_spline,
                                                          k=min(2, len(valid_loads_for_spline)-1))
                    std_smooth = std_spline(x_smooth)

                    # 绘制平滑曲线
                    plt.plot(x_smooth, y_smooth, color=color, linewidth=2,
                            label=f'{algo} (平均值)')

                    # 绘制标准差区域
                    plt.fill_between(x_smooth,
                                   y_smooth - std_smooth,
                                   y_smooth + std_smooth,
                                   alpha=0.1, color=color)
                except Exception as e:
                    print(f"Warning: Could not create spline for {algo} - {title}: {e}")
                    # 如果样条插值失败，就只绘制线性连接
                    plt.plot(valid_loads_for_spline, valid_means_for_spline,
                            color=color, linewidth=2, label=f'{algo} (平均值)', marker='o')
            else:
                # 如果点数太少，就直接连接点
                plt.plot(unique_loads, mean_values,
                        color=color, linewidth=2, label=f'{algo} (平均值)', marker='o')

        plt.xlabel('数据包数量')
        plt.xticks(range(200, 3001, 200))  # 设置x轴刻度为200, 400, 600, ..., 3000
        plt.ylabel(ylabel)
        plt.grid(True, alpha=0.3)
        plt.legend()

        # 保存图像
        plt.savefig(os.path.join(results_dir, f"{title.lower().replace(' ', '_')}_replotted.png"),
                   dpi=300, bbox_inches='tight')
        plt.close()

        # 打印统计信息
        print(f"\n{title} Comparison:")
        for algo, metrics_dict in [('DQN', metrics_dqn), ('最短路径', metrics_sp), ('CBDR', metrics_cbdr)]:
            data = metrics_dict[metric_key]
            valid_data = [d for d in data if np.isfinite(d)]
            if valid_data:
                print(f"\n{algo}:")
                print(f"Mean: {np.mean(valid_data):.3f}")
                print(f"Std: {np.std(valid_data):.3f}")
                print(f"Min: {np.min(valid_data):.3f}")
                print(f"Max: {np.max(valid_data):.3f}")
            else:
                print(f"\n{algo}: No valid data")

def plot_from_saved_data(data_path):
    """从保存的数据重新绘图"""
    print(f"正在加载数据: {data_path}")
    
    # 加载数据
    data = load_simulation_data(data_path)
    
    # 提取数据
    metrics_dqn = data['metrics_dqn']
    metrics_sp = data['metrics_sp']
    metrics_cbdr = data['metrics_cbdr']
    network_load = np.array(data['network_load'])
    trials = data['trials']
    config_info = data.get('config_info', {})
    
    print(f"数据加载成功!")
    print(f"配置信息: {config_info}")
    print(f"网络负载范围: {network_load}")
    print(f"试验次数: {trials}")
    
    # 创建新的图片保存目录
    base_dir = os.path.dirname(data_path)
    plot_dir = os.path.join(base_dir, "replotted_figures")
    if not os.path.exists(plot_dir):
        os.makedirs(plot_dir)
    
    # 重新绘图
    plot_comparison_from_data(network_load, trials, metrics_dqn, metrics_sp, metrics_cbdr, plot_dir)
    print(f"图片已重新生成到: {plot_dir}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        data_path = sys.argv[1]
        if os.path.exists(data_path):
            plot_from_saved_data(data_path)
        else:
            print(f"错误: 文件不存在: {data_path}")
    else:
        print("用法: python plot_from_saved_data.py <数据文件路径>")
        print("例如: python plot_from_saved_data.py simulation_data/simulation_20231201_143022/simulation_data.pkl")
