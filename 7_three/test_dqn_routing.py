import sys
from datetime import datetime
import torch.nn.functional as F
import time
from our_env import *
import matplotlib.pyplot as plt
import matplotlib
import json
import numpy as np
import os
import scipy.interpolate as interp
import networkx as nx
from collections import defaultdict
import pickle
matplotlib.use('Agg')

plt.rcParams['font.sans-serif'] = ['SimSun']
plt.rcParams['font.serif'] = ['Times New Roman']
plt.rcParams['axes.unicode_minus'] = False

def load_trained_models(env, model_dir="saved_models"):
    """加载训练好的模型"""
    print("Loading trained models...")
    for i, nn in enumerate(env.dqn):
        model_path = os.path.join(model_dir, f"policy_net_node_{i}.pt")
        if os.path.exists(model_path):
            nn.policy_net.load_state_dict(torch.load(model_path, map_location=nn.device))
            nn.target_net.load_state_dict(nn.policy_net.state_dict())
        else:
            print(f"Warning: Model file not found for node {i}")
    print("Models loaded successfully")

def convert_to_json_serializable(obj):
    """将numpy数据类型转换为JSON可序列化的类型"""
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: convert_to_json_serializable(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_json_serializable(item) for item in obj]
    else:
        return obj

def save_simulation_data(metrics_dqn, metrics_sp, metrics_cbdr, network_load, trials, config_info):
    """保存仿真数据到文件"""
    # 创建数据文件夹
    data_dir = "simulation_data"
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)

    # 创建时间戳文件夹
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_dir = os.path.join(data_dir, f"simulation_{timestamp}")
    os.makedirs(save_dir)

    # 保存数据
    data = {
        'metrics_dqn': dict(metrics_dqn),
        'metrics_sp': dict(metrics_sp),
        'metrics_cbdr': dict(metrics_cbdr),
        'network_load': network_load.tolist() if hasattr(network_load, 'tolist') else list(network_load),
        'trials': trials,
        'config_info': config_info,
        'timestamp': timestamp
    }

    # 保存为pickle文件（推荐，保持数据类型）
    with open(os.path.join(save_dir, 'simulation_data.pkl'), 'wb') as f:
        pickle.dump(data, f)

    # 转换为JSON可序列化的格式并保存为JSON文件
    json_data = convert_to_json_serializable(data)
    with open(os.path.join(save_dir, 'simulation_data.json'), 'w', encoding='utf-8') as f:
        json.dump(json_data, f, ensure_ascii=False, indent=2)

    print(f"数据已保存到: {save_dir}")
    return save_dir

def load_simulation_data(data_path):
    """从文件加载仿真数据"""
    if data_path.endswith('.pkl'):
        with open(data_path, 'rb') as f:
            return pickle.load(f)
    elif data_path.endswith('.json'):
        with open(data_path, 'r', encoding='utf-8') as f:
            return json.load(f)

def visualize_network(env, load, save_dir):
    """3D可视化卫星网络和当前正在进行的路由"""
    fig = plt.figure(figsize=(15, 12))
    ax = fig.add_subplot(111, projection='3d')

    # 绘制所有边（卫星间链路）
    for edge in env.dynetwork._network.edges():
        pos1 = env._positions[edge[0]]
        pos2 = env._positions[edge[1]]
        # 根据链路使用情况设置颜色
        edge_usage = env.dynetwork._network[edge[0]][edge[1]].get('usage', 0)
        color = 'red' if edge_usage > 0 else 'blue'
        alpha = min(0.8, 0.2 + edge_usage * 0.6)
        ax.plot([pos1[0], pos2[0]],
               [pos1[1], pos2[1]],
               [pos1[2], pos2[2]],
               c=color, linewidth=0.5, alpha=alpha)

    # 绘制节点和当前的路由状态
    for node in env.dynetwork._network.nodes():
        pos = env._positions[node]
        queue = env.dynetwork._network.nodes[node]['sending_queue']
        receiving_queue = env.dynetwork._network.nodes[node]['receiving_queue']

        # 根据节点状态设置颜色
        if queue:  # 如果节点有数据包在发送队列中
            color = 'yellow'
            size = 30 + len(queue) * 2
        elif receiving_queue:  # 如果节点有数据包在接收队列中
            color = 'orange'
            size = 30 + len(receiving_queue) * 2
        else:  # 空闲节点
            color = 'skyblue'
            size = 20

        ax.scatter(*pos, c=color, s=size)

        # 显示节点ID和队列信息
        queue_info = f'{node}\n'
        if queue:
            queue_info += f'S:{len(queue)}'
        if receiving_queue:
            queue_info += f'R:{len(receiving_queue)}'
        ax.text(*pos, queue_info, fontsize=8)

    # 设置图形属性
    ax.set_title(f"Satellite Network State (Load: {load})\n"
                 f"Delivered: {env.dynetwork._deliveries}, "
                 f"Rejected: {env.dynetwork._rejections}\n"
                 f"Active Links: {sum(1 for _, _, data in env.dynetwork._network.edges(data=True) if data.get('usage', 0) > 0)}")
    ax.set_xlabel('X (km)')
    ax.set_ylabel('Y (km)')
    ax.set_zlabel('Z (km)')

    # 添加图例
    from matplotlib.lines import Line2D
    legend_elements = [
        Line2D([0], [0], marker='o', color='w', label='Idle Node', markerfacecolor='skyblue', markersize=10),
        Line2D([0], [0], marker='o', color='w', label='Sending Node', markerfacecolor='yellow', markersize=10),
        Line2D([0], [0], marker='o', color='w', label='Receiving Node', markerfacecolor='orange', markersize=10),
        Line2D([0], [0], color='blue', label='Idle Link', linewidth=1),
        Line2D([0], [0], color='red', label='Active Link', linewidth=1)
    ]
    ax.legend(handles=legend_elements, loc='upper right')

    ax.view_init(elev=30, azim=45)
    ax.set_box_aspect([1,1,1])

    # 保存图像
    plt.savefig(os.path.join(save_dir, f'network_state_load_{load}.png'),
                dpi=300, bbox_inches='tight')
    plt.close()

class ShortestPathRouter:
    def __init__(self, env, convergence_time=20):
        self.env = env
        self.network = env.dynetwork._network
        self.convergence_time = convergence_time  # 使用传入的收敛时间参数
        self.time_since_change = 0
        self.config = {'update_epsilon': False}
        self.last_network_state = None

    def update_convergence_timers(self):
        """更新收敛时间计数器
        最短路径路由器使用全局收敛时间，而不是节点级别的收敛时间
        """
        if self.time_since_change < self.convergence_time:
            self.time_since_change += 1

    def act(self, dqn, state, neighbors, current_node=None, destination_node=None, network=None):
        """路由决策函数，实现全局收敛时间

        参数顺序和名称与our_env.py中的调用一致
        """
        # 检查dqn参数是否为None（最短路径算法不使用dqn，所以可以忽略）

        # 如果还在收敛时间内，不进行路由
        if self.time_since_change < self.convergence_time:
            return None

        try:
            # 使用Dijkstra算法计算最短路径
            if network is None:
                network = self.network

            path = nx.shortest_path(network, source=current_node, target=destination_node, weight='edge_delay')
            if len(path) >= 2:
                next_hop = path[1]
                # 检查目标节点是否有容量接收
                if next_hop in network.nodes and 'sending_queue' in network.nodes[next_hop]:
                    next_queue = network.nodes[next_hop]['sending_queue']
                    if len(next_queue) < self.env.max_queue:
                        return next_hop
        except Exception as e:
            # 如果计算最短路径时出错，返回None
            pass

        return None

class DQNRouter:
    def __init__(self, env, agent, convergence_time=5):
        self.env = env
        self.agent = agent  # 原始DQN智能体
        self.network = env.dynetwork._network
        self.convergence_time = convergence_time  # DQN的收敛时间参数
        self.node_convergence = {}  # 每个节点的收敛时间计数器
        self.affected_nodes = set()  # 受链路变化影响的节点
        self.last_network_edges = set(self.network.edges())  # 上一次的网络边集合

        # 添加config属性，与our_env.py中的调用兼容
        self.config = {
            'update_epsilon': False,
            'epsilon': agent.config.get('epsilon', 0.0001),
            'decay_rate': agent.config.get('decay_rate', 1.0)
        }

        # 初始化所有节点的收敛时间计数器
        for node in self.network.nodes():
            self.node_convergence[node] = self.convergence_time  # 初始化为已收敛状态

    def update_convergence_timers(self):
        """更新所有节点的收敛时间计数器"""
        for node in self.network.nodes():
            if node in self.node_convergence and self.node_convergence[node] < self.convergence_time:
                self.node_convergence[node] += 1

    def act(self, dqn, state, neighbors, current_node=None, destination_node=None, network=None):
        """路由决策函数，实现分布式收敛时间

        参数顺序和名称与our_env.py中的调用一致
        """
        # 检查dqn参数是否为None
        if dqn is None:
            return None

        # 检查当前节点是否已收敛
        if current_node in self.node_convergence and self.node_convergence[current_node] < self.convergence_time:
            # 如果节点尚未收敛，不进行路由
            return None

        # 直接调用原始DQN智能体的act方法
        return self.agent.act(
            dqn,
            state,
            neighbors,
            current_node=current_node,
            destination_node=destination_node,
            network=network
        )

class CBDRouter:
    """基于缓存的动态路由算法 (Cache-Based Dynamic Routing)

    特点：
    1. 基于DQN算法，但不需要等待收敛时间
    2. 当链路中断时，使用缓存和备用路径避免丢包
    3. 选择Q值第二大的下一跳作为备用路径
    """
    def __init__(self, env, agent, cache_size=10):
        self.env = env
        self.agent = agent  # 原始DQN智能体
        self.network = env.dynetwork._network
        self.cache_size = cache_size  # 每个节点的缓存大小
        self.node_caches = {}  # 每个节点的缓存
        self.broken_links = set()  # 当前断开的链路
        self.last_network_edges = set(self.network.edges())  # 上一次的网络边集合
        self.backup_paths = {}  # 每个节点的备用路径
        self.affected_nodes = set()  # 受链路变化影响的节点
        self.node_convergence = {}  # 每个节点的收敛时间计数器

        # 初始化所有节点的收敛时间计数器
        for node in self.network.nodes():
            self.node_convergence[node] = 0  # CBDR不需要收敛时间，所以设置为0

        self.stats = {  # 统计信息
            'cache_hits': 0,  # 缓存命中次数
            'backup_path_used': 0,  # 备用路径使用次数
            'packets_rerouted': 0,  # 重新路由的数据包数量
            'packets_dropped': 0,  # 丢弃的数据包数量
        }

        # 添加convergence_time和time_since_change属性，为了保持接口一致性
        # CBDR不需要收敛时间，但为了与其他路由器兼容，我们提供了这些属性
        self.convergence_time = 0  # CBDR不需要收敛时间，所以设置为0
        self.time_since_change = 0  # CBDR不需要收敛时间，所以设置为0

        # 添加config属性，与our_env.py中的调用兼容
        self.config = {
            'update_epsilon': False,
            'epsilon': agent.config.get('epsilon', 0.0001),
            'decay_rate': agent.config.get('decay_rate', 1.0)
        }

        # 初始化每个节点的缓存
        for node in self.network.nodes():
            self.node_caches[node] = []  # 初始化为空缓存

    def update_convergence_timers(self):
        """空方法，为了保持接口一致性
        CBDR不需要收敛时间，但为了与其他路由器兼容，我们提供了这个空方法
        """
        pass  # CBDR不需要收敛时间

    def update_network_state(self):
        """更新网络状态，检测链路变化并更新断开的链路"""
        current_edges = set(self.network.edges())

        # 输出调试信息
        print(f"\n[DEBUG] CBDR: 当前边数: {len(current_edges)}, 上一次边数: {len(self.last_network_edges)}")

        # 检测添加和删除的边
        added_edges = current_edges - self.last_network_edges
        removed_edges = self.last_network_edges - current_edges

        # 输出调试信息
        print(f"[DEBUG] CBDR: 添加的边数: {len(added_edges)}, 删除的边数: {len(removed_edges)}")

        # 更新断开的链路
        self.broken_links = self.broken_links - added_edges  # 移除恢复的链路
        self.broken_links = self.broken_links | removed_edges  # 添加新断开的链路

        # 输出调试信息
        print(f"[DEBUG] CBDR: 当前断开的链路数: {len(self.broken_links)}")

        # 更新受影响的节点的备用路径
        affected_nodes = set()
        for edge in added_edges | removed_edges:
            affected_nodes.add(edge[0])
            affected_nodes.add(edge[1])

        # 输出调试信息
        print(f"[DEBUG] CBDR: 受影响的节点数: {len(affected_nodes)}")

        # 更新上一次的网络边集合
        self.last_network_edges = current_edges

        return added_edges, removed_edges, affected_nodes

    def get_backup_path(self, dqn, state, current_node, destination_node, neighbors):
        """获取备用路径（Q值第二大的下一跳）"""
        # 检查dqn参数是否为None
        if dqn is None:
            print(f"  - CBDR路由器: 无法获取备用路径: dqn参数为None")
            # 如果只有一个邻居，直接返回
            if len(neighbors) == 1:
                return neighbors[0], neighbors[0]
            # 如果有多个邻居，随机选择两个
            elif len(neighbors) >= 2:
                import random
                best_next_hop = random.choice(neighbors)
                second_best_next_hop = random.choice([n for n in neighbors if n != best_next_hop])
                return best_next_hop, second_best_next_hop
            # 如果没有邻居，返回None
            else:
                return None, None

        with torch.no_grad():
            # 获取所有邻居的Q值
            qvals = dqn.policy_net(state.float())
            neighbor_qvals = qvals[:, neighbors].squeeze(0)

            # 如果只有一个邻居，直接返回
            if len(neighbors) <= 1:
                return neighbors[0] if neighbors else None, None

            # 获取Q值最大和第二大的下一跳
            sorted_indices = torch.argsort(neighbor_qvals, descending=True)
            best_idx = sorted_indices[0].item()
            second_best_idx = sorted_indices[1].item()

            best_next_hop = neighbors[best_idx]
            second_best_next_hop = neighbors[second_best_idx]

            return best_next_hop, second_best_next_hop

    def add_to_cache(self, node, packet):
        """将数据包添加到节点的缓存中"""
        # 如果缓存已满，移除最早的数据包
        if len(self.node_caches[node]) >= self.cache_size:
            self.node_caches[node].pop(0)  # 移除最早的数据包

        # 添加新的数据包
        self.node_caches[node].append(packet)

    def act(self, dqn, state, neighbors, current_node=None, destination_node=None, network=None):
        """路由决策函数，实现基于缓存的动态路由

        参数顺序和名称与our_env.py中的调用一致
        """
        # 检查dqn参数是否为None
        if dqn is None:
            print(f"  - CBDR路由器: 数据包从 {current_node} 到 {destination_node} 的路由失败: dqn参数为None")
            self.stats['packets_dropped'] += 1
            return None

        # 如果没有邻居，返回None
        if not neighbors:
            self.stats['packets_dropped'] += 1
            return None

        try:
            # 获取最佳路径和备用路径
            best_next_hop, second_best_next_hop = self.get_backup_path(dqn, state, current_node, destination_node, neighbors)

            # 检查最佳路径是否可用（链路未断开）
            if (current_node, best_next_hop) not in self.broken_links and (best_next_hop, current_node) not in self.broken_links:
                # 最佳路径可用，直接返回
                return best_next_hop

            # 最佳路径不可用，使用备用路径
            print(f"[DEBUG] CBDR: 链路 ({current_node}, {best_next_hop}) 断开，使用备用路径 {second_best_next_hop}")
            self.stats['backup_path_used'] += 1

            # 检查备用路径是否可用
            if (current_node, second_best_next_hop) not in self.broken_links and (second_best_next_hop, current_node) not in self.broken_links:
                # 备用路径可用，返回
                self.stats['packets_rerouted'] += 1
                return second_best_next_hop

            # 备用路径也不可用，尝试其他邻居
            for neighbor in neighbors:
                if neighbor != best_next_hop and neighbor != second_best_next_hop:
                    if (current_node, neighbor) not in self.broken_links and (neighbor, current_node) not in self.broken_links:
                        # 找到可用的邻居，返回
                        print(f"[DEBUG] CBDR: 备用路径也断开，使用其他邻居 {neighbor}")
                        self.stats['packets_rerouted'] += 1
                        return neighbor

            # 所有路径都不可用，将数据包添加到缓存
            print(f"[DEBUG] CBDR: 所有路径都不可用，将数据包添加到缓存")
            # 注意：在实际实现中，这里应该将数据包添加到缓存
            # 但在测试中，我们只能返回None，表示数据包被缓存
            self.stats['cache_hits'] += 1
            return None
        except Exception as e:
            print(f"[ERROR] CBDR: {str(e)}")
            self.stats['packets_dropped'] += 1
            return None

    def update_convergence_timers(self):
        """更新所有节点的收敛时间计数器"""
        for node in self.network.nodes():
            if node in self.node_convergence and self.node_convergence[node] < self.convergence_time:
                self.node_convergence[node] += 1

    def act(self, dqn, state, neighbors, current_node=None, destination_node=None, network=None):
        """路由决策函数，实现分布式收敛时间

        参数顺序和名称与our_env.py中的调用一致
        """
        # 检查当前节点是否已收敛
        if current_node in self.node_convergence and self.node_convergence[current_node] < self.convergence_time:
            # 如果节点尚未收敛，不进行路由
            print(f"  - DQN路由器: 数据包从 {current_node} 到 {destination_node} 的路由被丢弃: 节点 {current_node} 尚未收敛 ({self.node_convergence[current_node]}/{self.convergence_time})")
            return None

        # 直接调用原始DQN智能体的act方法
        # 这样可以确保与our_env.py中的调用兼容
        return self.agent.act(
            dqn,
            state,
            neighbors,
            current_node=current_node,
            destination_node=destination_node,
            network=network
        )

def test_routing_comparison(shortest_path_convergence_time=20, dqn_convergence_time=2, cache_size=10):
    """比较DQN路由、最短路径路由和CBDR算法的性能

    Args:
        shortest_path_convergence_time: 最短路径算法的收敛时间，越大表示收敛越慢，丢包率越高
        dqn_convergence_time: DQN算法的收敛时间，越大表示收敛越慢，丢包率越高
        cache_size: CBDR算法的缓存大小，越大表示可以缓存的数据包越多
    """
    # 读取配置
    with open('Setting.json') as f:
        setting = json.load(f)

    # 初始化测试参数
    network_load = np.arange(
        setting["Simulation"]["test_network_load_min"],
        setting["Simulation"]["test_network_load_max"] + setting["Simulation"]["test_network_load_step_size"],
        setting["Simulation"]["test_network_load_step_size"]
    )
    trials = setting["Simulation"]["test_trials_per_load"]
    time_steps = setting["Simulation"]["max_allowed_time_step_per_episode"]

    # 创建结果目录
    results_dir = os.path.join(os.path.dirname(__file__), 'test_results')
    if not os.path.exists(results_dir):
        os.makedirs(results_dir)

    # 初始化性能指标数组
    metrics_dqn = defaultdict(list)
    metrics_sp = defaultdict(list)
    metrics_cbdr = defaultdict(list)  # 添加CBDR算法的指标字典

    # 测试三种算法
    for algorithm in ['DQN', 'ShortestPath', 'CBDR']:
        print(f"\n=== Testing {algorithm} Routing ===")

        # 初始化环境
        env = dynetworkEnv()
        current_time_step = 0  # 添加时间步计数器

        # 计算和显示当前网络的边数量和断链概率
        total_edges = len(env.dynetwork._network.edges())
        min_removal = env.min_edge_removal
        max_removal = env.max_edge_removal
        avg_removal = (min_removal + max_removal) / 2
        edge_removal_prob = avg_removal / total_edges

        # 初始化原始DQN智能体（所有算法都需要）
        dqn_agent = QAgent(env.dynetwork)
        load_trained_models(env)
        dqn_agent.config['epsilon'] = 0.0001
        dqn_agent.config['decay_rate'] = 1

        if algorithm == 'DQN':
            # 创建分布式收敛时间的DQN路由器
            agent = DQNRouter(env, dqn_agent, convergence_time=dqn_convergence_time)  # 使用传入的DQN收敛时间
            print(f"- DQN分布式收敛时间: {dqn_convergence_time} 时间步")
        elif algorithm == 'ShortestPath':
            # 创建最短路径路由器，传入收敛时间参数
            agent = ShortestPathRouter(env, convergence_time=shortest_path_convergence_time)
            print(f"- 最短路径全局收敛时间: {shortest_path_convergence_time} 时间步")
        else:  # CBDR
            # 创建基于缓存的动态路由器
            agent = CBDRouter(env, dqn_agent, cache_size=cache_size)
            print(f"- CBDR缓存大小: {cache_size} 数据包")
            print(f"- CBDR不需要收敛时间，使用缓存和备用路径避免丢包")

        # 对不同网络负载进行测试
        for i in range(len(network_load)):
            curLoad = network_load[i]
            print(f"\n---------- Testing with load: {curLoad} ----------")

            for currTrial in range(trials):
                print(f"Trial {currTrial + 1}/{trials}")
                env.reset(curLoad)
                current_time_step = 0

                # 在每个时间步进行路由
                for t in range(time_steps):
                    current_time_step = t

                    # 记录当前网络状态
                    current_edge_count = len(env.dynetwork._network.edges())

                    if algorithm == 'DQN':
                        # 先保存当前的边集合，用于检测变化
                        current_edges_before = set(env.dynetwork._network.edges())

                        # 更新环境（包括边的变化）
                        env.change_network()

                        # 获取更新后的边集合
                        current_edges_after = set(env.dynetwork._network.edges())

                        # 手动计算添加和删除的边
                        added_edges = current_edges_after - current_edges_before
                        removed_edges = current_edges_before - current_edges_after

                        # 清空受影响节点集合
                        agent.affected_nodes.clear()

                        # 只添加受链路变化直接影响的节点（断开或恢复的链路的两端节点）
                        for edge in added_edges | removed_edges:
                            agent.affected_nodes.add(edge[0])
                            agent.affected_nodes.add(edge[1])

                        # 输出受影响的节点
                        if agent.affected_nodes:
                            print(f"[DEBUG] 受影响的节点: {agent.affected_nodes}")

                        # 重置受影响节点的收敛时间计数器
                        for node in agent.affected_nodes:
                            agent.node_convergence[node] = 0

                        # 更新上一次的网络边集合
                        agent.last_network_edges = current_edges_after

                        # 输出调试信息
                        print(f"\n[DEBUG] 当前边数: {len(current_edges_after)}, 上一次边数: {len(current_edges_before)}")
                        print(f"[DEBUG] 添加的边数: {len(added_edges)}, 删除的边数: {len(removed_edges)}")
                        print(f"[DEBUG] 受影响的节点数: {len(agent.affected_nodes)}")

                        # 输出当前DQN收敛状态
                        print(f"\n==== Time step {t}: DQN Convergence Status =====")
                        converged_nodes = [node for node, timer in agent.node_convergence.items() if timer >= agent.convergence_time]
                        unconverged_nodes = [node for node, timer in agent.node_convergence.items() if timer < agent.convergence_time]
                        print(f"  - DQN分布式收敛时间: {agent.convergence_time} 时间步")
                        print(f"  - 已收敛节点数量: {len(converged_nodes)}/{len(agent.node_convergence)}")

                        # 显示分布式收敛的优势
                        if added_edges or removed_edges:
                            print(f"  - 链路变化: 添加 {len(added_edges)} 条，删除 {len(removed_edges)} 条")
                            print(f"  - 只有 {len(agent.affected_nodes)} 个节点受到影响需要重置收敛时间，而不是所有节点")
                            print(f"  - 这是DQN分布式收敛的优势，可以显著减少丢包率")

                        if unconverged_nodes:
                            print(f"  - 当前未收敛节点 ({len(unconverged_nodes)}个):")
                            for node in unconverged_nodes[:5]:  # 只显示前5个节点，避免输出过多
                                print(f"    - 节点 {node}: {agent.node_convergence[node]}/{agent.convergence_time}")
                            if len(unconverged_nodes) > 5:
                                print(f"    - ... 及其他 {len(unconverged_nodes)-5} 个节点")
                            print(f"  - 只有这些节点发出的数据包将被丢弃，其他节点可以正常路由")
                        else:
                            print(f"  - 所有节点均已收敛，可以正常路由数据包")

                        # 更新所有节点的收敛时间计数器
                        agent.update_convergence_timers()

                        # 使用DQN进行路由
                        env.updateWhole(agent, learn=False)
                    elif algorithm == 'CBDR':
                        # 先保存当前的边集合，用于检测变化
                        current_edges_before = set(env.dynetwork._network.edges())

                        # 更新环境（包括边的变化）
                        env.change_network()

                        # 获取更新后的边集合
                        current_edges_after = set(env.dynetwork._network.edges())

                        # 手动计算添加和删除的边
                        added_edges = current_edges_after - current_edges_before
                        removed_edges = current_edges_before - current_edges_after

                        # 更新断开的链路
                        agent.broken_links = agent.broken_links - added_edges  # 移除恢复的链路
                        agent.broken_links = agent.broken_links | removed_edges  # 添加新断开的链路

                        # 更新上一次的网络边集合
                        agent.last_network_edges = current_edges_after

                        # 使用CBDR进行路由
                        env.updateWhole(agent, learn=False)
                    else:  # ShortestPath
                        # 使用节点索引而不是id
                        current_node_idx = t % len(env.dynetwork._network.nodes())
                        next_node_idx = (t + 1) % len(env.dynetwork._network.nodes())

                        # 更新环境（包括边的变化）
                        env.change_network()

                        # 检查网络拓扑是否发生变化
                        new_edge_count = len(env.dynetwork._network.edges())

                        # 对于最短路径路由器，使用全局收敛时间机制
                        # 检查网络拓扑是否发生变化
                        new_edges = set(env.dynetwork._network.edges())
                        current_edges = set(agent.last_network_edges) if hasattr(agent, 'last_network_edges') else set()

                        # 计算添加和删除的边
                        added_edges = new_edges - current_edges
                        removed_edges = current_edges - new_edges

                        # 如果有边被添加或删除，则网络拓扑发生了变化
                        if added_edges or removed_edges:
                            # 重置收敛时间计数器
                            agent.time_since_change = 0
                            agent.last_network_edges = new_edges
                        else:
                            # 如果网络拓扑没有变化，增加收敛时间计数器
                            agent.time_since_change += 1

                        # 显示全局收敛的劣势
                        if added_edges or removed_edges:
                            print(f"  - 链路变化: 添加 {len(added_edges)} 条，删除 {len(removed_edges)} 条")
                            affected_nodes_count = len(set([node for edge in (added_edges | removed_edges) for node in edge]))
                            print(f"  - 尽管只有 {affected_nodes_count} 个节点受到影响，但所有节点都需要等待收敛")
                            print(f"  - 这是最短路径全局收敛的劣势，会导致更高的丢包率")

                        if agent.time_since_change < agent.convergence_time:
                            print(f"  - 路由算法尚未收敛，所有节点的数据包都将被丢弃")
                        else:
                            print(f"  - 路由算法已收敛，可以正常路由数据包")

                        # 输出详细的网络变化信息
                        if added_edges or removed_edges:
                            print(f"  - 网络拓扑发生变化: 边数 {current_edge_count} -> {new_edge_count}")
                            if removed_edges:
                                # 只显示前5个断开的链路，避免输出过多
                                removed_list = list(removed_edges)
                                display_edges = removed_list[:5]
                                print(f"  - 断开的链路 ({len(removed_edges)}条): {display_edges}" +
                                      (f" ... 及其他{len(removed_edges)-5}条" if len(removed_edges) > 5 else ""))
                            if added_edges:
                                # 只显示前5个恢复的链路，避免输出过多
                                added_list = list(added_edges)
                                display_edges = added_list[:5]
                                print(f"  - 恢复的链路 ({len(added_edges)}条): {display_edges}" +
                                      (f" ... 及其他{len(added_edges)-5}条" if len(added_edges) > 5 else ""))



                        # 使用最短路径进行路由
                        node_queue_lengths = []
                        num_nodes_at_capacity = 0

                        # 遍历所有节点
                        for node in env.dynetwork._network.nodes():
                            queue = env.dynetwork._network.nodes[node]['sending_queue']
                            queue_size = len(queue)

                            # 更新队列长度统计
                            if queue_size > 0:
                                node_queue_lengths.append(queue_size)
                                if queue_size >= env.max_transmit:
                                    num_nodes_at_capacity += 1

                            # 尝试发送队列中的数据包（考虑发送容量限制）
                            packets_to_send = queue[:env.max_transmit]
                            for packet_id in packets_to_send:
                                packet = env.dynetwork._packets.packetList[packet_id]
                                current_pos = packet.get_curPos()
                                dest_pos = packet.get_endPos()

                                # 获取下一跳
                                next_hop = agent.act(
                                    env.dqn[dest_pos] if hasattr(env, 'dqn') and dest_pos in env.dqn else None,
                                    None,  # 状态在act方法中生成
                                    list(env.dynetwork._network.neighbors(current_pos)),  # 邻居列表
                                    current_node=current_pos,
                                    destination_node=dest_pos,
                                    network=env.dynetwork._network
                                )

                                if next_hop is not None:
                                    # 检查目标节点是否有容量接收
                                    if not env.is_capacity(env.dynetwork, next_hop):
                                        # 设置当前处理的包和队列
                                        env.packet = packet_id
                                        env.curr_queue = queue
                                        # 尝试发送数据包
                                        reward, remaining, curr_queue, action = env.step(next_hop)
                                    else:
                                        # 目标节点队列满，增加拒绝计数
                                        env.dynetwork._rejections += 1
                                else:
                                    # 没有可用路径或网络未收敛，增加拒绝计数
                                    env.dynetwork._rejections += 1

                        # 更新环境状态
                        if node_queue_lengths:
                            avg_q_len = np.mean(node_queue_lengths)
                            env.dynetwork._avg_q_len_arr.append(avg_q_len)
                            # 检查是否有_max_queue_length属性
                            if hasattr(env.dynetwork, '_max_queue_length'):
                                env.dynetwork._max_queue_length = max(
                                    env.dynetwork._max_queue_length,
                                    max(node_queue_lengths)
                                )
                            else:
                                # 如果没有该属性，创建它
                                env.dynetwork._max_queue_length = max(node_queue_lengths)

                        # 更新其他环境状态
                        env.update_queues()
                        env.update_time()
                        env.purgatory()

                    # 检查是否所有数据包都已投递
                    if (env.dynetwork._deliveries >= (env.npackets + env.dynetwork._max_initializations)):
                        print(f"All packets delivered in {t+1} steps")
                        break

                # 收集性能指标
                if algorithm == 'DQN':
                    metrics = metrics_dqn
                elif algorithm == 'ShortestPath':
                    metrics = metrics_sp
                else:  # CBDR
                    metrics = metrics_cbdr

                # 确保有效的平均传输时间
                if env.dynetwork._delivery_times:
                    metrics['avg_deliv'].append(env.calc_avg_delivery())
                else:
                    metrics['avg_deliv'].append(0)

                # 其他指标
                metrics['maxNumPkts'].append(env.dynetwork._max_queue_length)
                if env.dynetwork._avg_q_len_arr:
                    metrics['avg_q_len'].append(np.mean(env.dynetwork._avg_q_len_arr))
                else:
                    metrics['avg_q_len'].append(0)

                # 计算丢包率
                total_packets = env.dynetwork._initializations + env.npackets
                if total_packets > 0:
                    metrics['packet_loss_rate'].append(
                        1.0 - (env.dynetwork._deliveries / total_packets)
                    )
                else:
                    metrics['packet_loss_rate'].append(0)

    # 保存仿真数据
    config_info = {
        'shortest_path_convergence_time': shortest_path_convergence_time,
        'dqn_convergence_time': dqn_convergence_time,
        'cache_size': cache_size,
        'time_steps': time_steps,
        'network_load_range': f"{network_load[0]}-{network_load[-1]}",
        'step_size': network_load[1] - network_load[0] if len(network_load) > 1 else 0
    }

    # 保存数据并获取保存目录
    data_save_dir = save_simulation_data(metrics_dqn, metrics_sp, metrics_cbdr,
                                        network_load, trials, config_info)

    # 绘制比较图（使用数据保存目录作为图片保存目录）
    plot_comparison(network_load, trials, metrics_dqn, metrics_sp, metrics_cbdr, data_save_dir)

def plot_comparison(network_load, trials, metrics_dqn, metrics_sp, metrics_cbdr, results_dir):
    """绘制DQN、最短路径和CBDR的性能对比图"""
    # 设置中文字体和字体大小
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams.update({
        'font.size': 20,           # 基础字体大小
        'axes.titlesize': 24,      # 标题字体大小
        'axes.labelsize': 22,      # 坐标轴标签字体大小
        'xtick.labelsize': 18,     # x轴刻度字体大小
        'ytick.labelsize': 18,     # y轴刻度字体大小
        'legend.fontsize': 18      # 图例字体大小
    })

    metrics = [
        ("平均时延", 'avg_deliv', "平均时延 (ms)"),
        ("最大队列长度", 'maxNumPkts', "每个节点的最大数据包数量"),
        ("平均队列长度", 'avg_q_len', "每个节点的平均数据包数量"),
        ("丢包率", 'packet_loss_rate', "丢包率 ")
    ]

    for title, metric_key, ylabel in metrics:
        plt.figure(figsize=(12, 7))
        plt.title(f"{title}")

        # 绘制三种算法的数据
        for algo, metrics_dict, color, marker in [
            ('DQN', metrics_dqn, 'blue', 'o'),
            ('最短路径', metrics_sp, 'red', '^'),
            ('CBDR', metrics_cbdr, 'green', 's')
        ]:
            data = metrics_dict[metric_key]

            # 移除任何 NaN 或 Inf 值
            valid_data = []
            valid_loads = []
            for d, l in zip(data, np.repeat(network_load, trials)):
                if np.isfinite(d):  # 只保留有限的数值
                    valid_data.append(d)
                    valid_loads.append(l)

            if not valid_data:  # 如果没有有效数据，跳过这个指标
                print(f"Warning: No valid data for {algo} - {title}")
                continue

            # 散点图
            plt.scatter(valid_loads, valid_data,
                       alpha=0.3, color=color, marker=marker)

            # 计算平均值
            unique_loads = np.unique(valid_loads)
            mean_values = []
            std_values = []

            for ul in unique_loads:
                values = [d for d, l in zip(valid_data, valid_loads) if l == ul]
                if values:  # 确保有数据
                    mean_values.append(np.mean(values))
                    std_values.append(np.std(values))
                else:
                    mean_values.append(np.nan)
                    std_values.append(np.nan)

            # 移除任何 NaN 值以进行插值
            valid_indices = ~np.isnan(mean_values)
            if np.sum(valid_indices) > 3:  # 需要至少4个点才能进行三次样条插值
                valid_loads_for_spline = unique_loads[valid_indices]
                valid_means_for_spline = np.array(mean_values)[valid_indices]
                valid_stds_for_spline = np.array(std_values)[valid_indices]

                # 平滑曲线
                x_smooth = np.linspace(min(valid_loads_for_spline),
                                     max(valid_loads_for_spline), 50)
                try:
                    # 使用较低阶数的样条插值来避免过拟合
                    spline = interp.make_interp_spline(valid_loads_for_spline,
                                                      valid_means_for_spline,
                                                      k=min(3, len(valid_loads_for_spline)-1))
                    y_smooth = spline(x_smooth)

                    # 对标准差也进行插值
                    std_spline = interp.make_interp_spline(valid_loads_for_spline,
                                                          valid_stds_for_spline,
                                                          k=min(3, len(valid_loads_for_spline)-1))
                    std_smooth = std_spline(x_smooth)

                    # 绘制平滑曲线
                    plt.plot(x_smooth, y_smooth, color=color, linewidth=2,
                            label=f'{algo} (平均值)')

                    # 绘制标准差区域
                    plt.fill_between(x_smooth,
                                   y_smooth - std_smooth,
                                   y_smooth + std_smooth,
                                   alpha=0.1, color=color)
                except Exception as e:
                    print(f"Warning: Could not create spline for {algo} - {title}: {e}")
                    # 如果样条插值失败，就只绘制线性连接
                    plt.plot(valid_loads_for_spline, valid_means_for_spline,
                            color=color, linewidth=2, label=f'{algo} (Mean)')
            else:
                # 如果点数太少，就直接连接点
                plt.plot(unique_loads, mean_values,
                        color=color, linewidth=2, label=f'{algo} (Mean)')

        plt.xlabel('数据包数量')
        plt.xticks(range(200,3001,200))
        plt.ylabel(ylabel)
        plt.grid(True, alpha=0.3)
        plt.legend()

        # 保存图像
        plt.savefig(os.path.join(results_dir, f"{title.lower().replace(' ', '_')}.png"),
                   dpi=300, bbox_inches='tight')
        plt.close()

        # 打印统计信息
        print(f"\n{title} Comparison:")
        for algo, metrics_dict in [('DQN', metrics_dqn), ('最短路径', metrics_sp), ('CBDR', metrics_cbdr)]:
            data = metrics_dict[metric_key]
            valid_data = [d for d in data if np.isfinite(d)]
            if valid_data:
                print(f"\n{algo}:")
                print(f"Mean: {np.mean(valid_data):.3f}")
                print(f"Std: {np.std(valid_data):.3f}")
                print(f"Min: {np.min(valid_data):.3f}")
                print(f"Max: {np.max(valid_data):.3f}")
            else:
                print(f"\n{algo}: No valid data")

def test_shortest_path_routing(env, network_load, trials=1):
    """测试最短路径路由性能"""
    delivery_times = []
    max_queue_lengths = []
    avg_queue_lengths = []
    packet_loss_rates = []

    sp_router = ShortestPathRouter(env)

    for _ in range(trials):
        print(f"Trial {_ + 1}/{trials}")
        env.reset()
        sp_router.time_since_change = 0  # 重置收敛时间计数器

        # 运行最短路径路由
        while not env.done:
            env.router(sp_router, will_learn=False)

        # 收集统计数据
        if env.dynetwork._deliveries > 0:
            avg_delivery_time = sum(env.dynetwork._delivery_times) / env.dynetwork._deliveries
            delivery_times.append(avg_delivery_time)

        max_queue_lengths.append(env.dynetwork._max_queue_length)
        avg_queue_lengths.append(env.dynetwork._avg_queue_length)

        # 计算丢包率
        total_packets = env.dynetwork._initializations
        delivered_packets = env.dynetwork._deliveries
        loss_rate = 1 - (delivered_packets / total_packets if total_packets > 0 else 0)
        packet_loss_rates.append(loss_rate)

    return {
        'delivery_times': delivery_times,
        'max_queue_lengths': max_queue_lengths,
        'avg_queue_lengths': avg_queue_lengths,
        'packet_loss_rates': packet_loss_rates
    }

if __name__ == "__main__":
    import argparse

    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='测试DQN路由和最短路径路由的性能')
    parser.add_argument('--convergence-time', type=int, default=20,
                        help='最短路径算法的收敛时间，越大表示收敛越慢，丢包率越高（默认：20）')
    parser.add_argument('--dqn-convergence-time', type=int, default=5,
                        help='DQN算法的分布式收敛时间，越大表示收敛越慢，丢包率越高（默认：5）')
    parser.add_argument('--cache-size', type=int, default=10,
                        help='CBDR算法的缓存大小，越大表示可以缓存的数据包越多（默认：10）')
    parser.add_argument('--edge-removal', type=int, default=None,
                        help='每次删除的边数量，设置该值会同时修改min_edge_removal和max_edge_removal（默认：使用Setting.json中的设置）')

    # 解析命令行参数
    args = parser.parse_args()

    # 如果指定了边删除数量，修改Setting.json文件
    if args.edge_removal is not None:
        with open('Setting.json', 'r') as f:
            setting = json.load(f)

        # 修改边删除范围
        setting['NETWORK']['min_edge_removal'] = args.edge_removal
        setting['NETWORK']['max_edge_removal'] = args.edge_removal

        # 保存修改后的设置
        with open('Setting.json', 'w') as f:
            json.dump(setting, f, indent=2)

        print(f"边删除数量设置为: {args.edge_removal}")

    # 运行测试，传入收敛时间和缓存大小参数
    print(f"最短路径算法收敛时间设置为: {args.convergence_time}")
    print(f"DQN算法分布式收敛时间设置为: {args.dqn_convergence_time}")
    print(f"CBDR算法缓存大小设置为: {args.cache_size}")
    test_routing_comparison(
        shortest_path_convergence_time=args.convergence_time,
        dqn_convergence_time=args.dqn_convergence_time,
        cache_size=args.cache_size
    )